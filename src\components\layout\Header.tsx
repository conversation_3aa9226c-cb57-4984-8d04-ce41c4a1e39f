import React from 'react';
import { Bell, User } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useDashboard } from '../../contexts/DashboardContext';
import ThemeToggle from '../common/ThemeToggle';

const Header: React.FC = () => {
  const { user } = useAuth();
  const { activeTab } = useDashboard();

  // Page title mapping
  const getPageTitle = (tab: string): { title: string; subtitle: string } => {
    switch (tab) {
      case 'dashboard':
        return {
          title: 'Dashboard Overview',
          subtitle: 'Welcome back! Here\'s your insurance portfolio summary.'
        };
      case 'policy-selection':
        return {
          title: 'Policy Selection',
          subtitle: 'Select and manage your insurance policies.'
        };
      case 'illustrations':
      case 'as-is':
      case 'face-amount':
      case 'premium':
      case 'income':
      case 'loan-repayment':
      case 'interest-rate':
      case 'policy-lapse':
        return {
          title: 'Policy Illustration',
          subtitle: 'Configure and analyze comprehensive policy scenarios with advanced modeling.'
        };
      case 'selected-scenarios':
        return {
          title: 'Selected Scenarios',
          subtitle: 'View all selected scenarios grouped by illustration.'
        };
      case 'analysis-reports':
        return {
          title: 'Analysis Reports',
          subtitle: 'Comprehensive analysis of your selected insurance policy scenarios.'
        };
      case 'settings':
        return {
          title: 'Settings',
          subtitle: 'Manage your account preferences and application settings.'
        };
      default:
        return {
          title: 'Life Insurance Manager',
          subtitle: 'Professional insurance policy management system.'
        };
    }
  };

  const { title, subtitle } = getPageTitle(activeTab);

  return (
    <header className="bg-white dark:bg-[#121212] border-b border-gray-200 dark:border-gray-800 px-6 py-4 transition-all duration-300 shadow-sm dark:shadow-lg">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{title}</h1>
            <p className="text-gray-600 dark:text-gray-400 text-sm">{subtitle}</p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <button className="p-2 text-gray-500 dark:text-white/70 hover:text-gray-700 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200">
            <Bell className="w-5 h-5" />
          </button>
          <ThemeToggle />
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center shadow-md">
              <User className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">{user?.name}</p>
              <p className="text-xs text-gray-500 dark:text-white/70">Administrator</p>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;