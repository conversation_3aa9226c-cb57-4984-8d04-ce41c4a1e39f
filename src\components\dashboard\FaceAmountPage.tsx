import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Save, RotateCcw, BarChart3, TrendingUp, User, FileText, DollarSign, Calendar, AlertTriangle } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Notification from '../common/Notification';
import { useDashboard } from '../../contexts/DashboardContext';

const FaceAmountPage = () => {
  // Add dashboard context for policy info
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario, scenarios, selectedScenarios } = useDashboard();

  // Mock customer data - in real app this would come from props or context
  const [customerData] = useState({
    name: '<PERSON>',
    customer_id: 'CUST001',
    policy_number: 'POL123456',
    coverage: '$500,000',
    premium: '$2,500/year'
  });

  const [selectedPolicy] = useState({
    name: 'Universal Life',
    coverage: '$500,000',
    premium: '$2,500/year'
  });

  const [faceAmountData, setFaceAmountData] = useState({
    current_death_benefit: 500000,
    want_to_change: false,
    change_immediately: false,
    change_amount: 0,
    change_by_years: false,
    change_year: '',
    death_benefit_option: 'A',
    switch_option: false,
    switch_immediately: false,
    switch_future_year: false,
    switch_year: '',
    switch_condition: false,
    condition_age: '',
    condition_cash_value: '',
    keep_current_option: true,
    option_b_to_a: false,
    option_b_switch_immediately: false,
    option_b_switch_future: false,
    option_b_switch_year: '',
    option_b_switch_condition: false,
    option_b_condition_age: '',
    option_b_condition_cash_value: '',
    keep_option_b: true
  });

  type FaceAmountScenario = {
    id: number;
    timestamp: string;
    customer_name: string;
    policy_number: string;
    data: typeof faceAmountData;
    summary: string[];
  };
  const [faceAmountHistory, setFaceAmountHistory] = useState<FaceAmountScenario[]>([]);
  const [showIllustration, setShowIllustration] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Show notification temporarily
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    setNotification({ message, type });
  };

  // Update form data
  const updateFormData = (field: keyof typeof faceAmountData, value: any) => {
    setFaceAmountData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Save scenario
  const saveScenario = async () => {
    setIsSaving(true);
    try {
      const now = new Date();
      const scenarioEntry = {
        id: (Date.now().toString() + Math.random()),
        name: `Face Amount Scenario - ${(selectedCustomerData?.name || customerData.name)}`,
        policyId: selectedCustomerData?.policyNumber || customerData.policy_number,
        asIsDetails: `Current Death Benefit: $${faceAmountData.current_death_benefit.toLocaleString()}`,
        whatIfOptions: generateScenarioSummary(),
        category: 'face-amount' as const,
        keyPoints: generateScenarioSummary(),
        impact: 'neutral',
        data: { ...faceAmountData },
        createdAt: now,
        updatedAt: now,
      };
      await addScenario(scenarioEntry);
      showNotification('AS-IS configuration saved successfully and added to Selected Scenarios!');
    } catch (error) {
      showNotification('Error saving scenario!', 'error');
      console.error('Error saving Face Amount scenario:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Generate scenario summary
  const generateScenarioSummary = () => {
    const summary = [];

    // Current face amount
    summary.push(`Current Face Amount Death Benefit: $${faceAmountData.current_death_benefit.toLocaleString()}`);

    // Face amount changes
    if (faceAmountData.change_immediately) {
      summary.push(`Change face amount to $${faceAmountData.change_amount.toLocaleString()} now`);
    }
    if (faceAmountData.change_by_years) {
      summary.push(`Change face amount to $${faceAmountData.change_amount.toLocaleString()} starting from age ${faceAmountData.change_year}`);
    }
    if (faceAmountData.want_to_change) {
      summary.push("Vary amount by age schedule selected");
    }

    // Option A to B switch
    if (faceAmountData.switch_immediately) {
      summary.push("Switch from Option A to B now");
    }
    if (faceAmountData.switch_future_year) {
      summary.push(`Switch from Option A to B starting from age ${faceAmountData.switch_year}`);
    }

    // Option B to A switch
    if (faceAmountData.option_b_switch_immediately) {
      summary.push("Switch from Option B to A now");
    }
    if (faceAmountData.option_b_switch_future) {
      summary.push(`Switch from Option B to A starting from age ${faceAmountData.option_b_switch_year}`);
    }

    return summary.length > 1 ? summary : ["Current face amount configuration - no changes selected"];
  };

  // Reset form
  const resetForm = () => {
    setFaceAmountData({
      current_death_benefit: 500000,
      want_to_change: false,
      change_immediately: false,
      change_amount: 0,
      change_by_years: false,
      change_year: '',
      death_benefit_option: 'A',
      switch_option: false,
      switch_immediately: false,
      switch_future_year: false,
      switch_year: '',
      switch_condition: false,
      condition_age: '',
      condition_cash_value: '',
      keep_current_option: true,
      option_b_to_a: false,
      option_b_switch_immediately: false,
      option_b_switch_future: false,
      option_b_switch_year: '',
      option_b_switch_condition: false,
      option_b_condition_age: '',
      option_b_condition_cash_value: '',
      keep_option_b: true
    });
    showNotification('Form reset!');
  };

  // Generate illustration data
  const generateIllustrationData = () => {
    const years = Array.from({ length: 20 }, (_, i) => 2024 + i);
    
    return years.map(year => {
      const yearsFromNow = year - 2024;
      const currentValue = faceAmountData.current_death_benefit * Math.pow(1.02, yearsFromNow);
      const newValue = faceAmountData.want_to_change && faceAmountData.change_immediately
        ? faceAmountData.change_amount * Math.pow(1.02, yearsFromNow)
        : currentValue;
      
      return {
        year,
        currentBenefit: Math.round(currentValue),
        newBenefit: Math.round(newValue)
      };
    });
  };

  // Generate illustration
  const generateIllustration = () => {
    setShowIllustration(true);
    showNotification('Face Amount illustration generated successfully!');
  };

  // Load scenario
  const loadScenario = (scenario: any) => {
    setFaceAmountData({ ...scenario.data });
    showNotification(`Scenario ${scenario.id} loaded!`);
  };



  // Filter selected scenarios for this category
  const selectedFaceAmountScenarios = scenarios.filter(
    s => selectedScenarios.includes(s.id) && s.category === 'face-amount'
  );

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="space-y-6">
        {/* <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">FACE AMOUNT ILLUSTRATION</h1>
          <p className="text-gray-600 dark:text-gray-400">Configure the face amount and death benefit options for the selected policy.</p>
        </div> */}

        {/* Notification */}
        {notification && (
          <Notification
            message={notification.message}
            type={notification.type}
            onClose={() => setNotification(null)}
          />
        )}

        {/* Show message if no policy is selected */}
        {(!selectedCustomerData || !selectedPolicyData) ? (
          <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
                <p className="text-yellow-700 dark:text-yellow-300">
                  Please go to the Policy Selection tab first to search and select a customer policy before configuring the Face Amount illustration.
                </p>
                <Button
                  onClick={() => setActiveTab('policy-selection')}
                  variant="outline"
                  className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
                >
                  Go to Policy Selection
                </Button>
              </div>
            </div>
          </Card>
        ) : (
          <>
            {/* Section 1: Current Face Amount Death Benefit */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              1. Your current (Face Amount) Death Benefit is 
            </h3>

            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-bold text-black mb-2">
                  Current Face Amount Death Benefit:
                </label>
                <input
                  type="number"
                  value={faceAmountData.current_death_benefit}
                  onChange={(e) => updateFormData('current_death_benefit', parseInt(e.target.value) || 0)}
                  className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                  step="10000"
                  placeholder="Enter current death benefit amount"
                />
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg border">
              <h4 className="font-bold text-black mb-4">a. Change to New Face amount ____ now or starting from age</h4>

              <div className="space-y-4">
                <div>
                  <label className="flex items-center text-black font-semibold mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.change_immediately}
                      onChange={(e) => updateFormData('change_immediately', e.target.checked)}
                      className="mr-2"
                    />
                    Change to New Face Amount now
                  </label>
                  {faceAmountData.change_immediately && (
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div>
                        <label className="block text-sm font-bold text-black mb-1">
                          New Face Amount:
                        </label>
                        <input
                          type="number"
                          value={faceAmountData.change_amount}
                          onChange={(e) => updateFormData('change_amount', parseInt(e.target.value) || 0)}
                          className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          step="10000"
                          placeholder="Enter new face amount"
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <label className="flex items-center text-black font-semibold mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.change_by_years}
                      onChange={(e) => updateFormData('change_by_years', e.target.checked)}
                      className="mr-2"
                    />
                    Change starting from age
                  </label>
                  {faceAmountData.change_by_years && (
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div>
                        <label className="block text-sm font-bold text-black mb-1">Starting Age:</label>
                        <select
                          value={faceAmountData.change_year}
                          onChange={(e) => updateFormData('change_year', e.target.value)}
                          className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                        >
                          <option value="">Select Age</option>
                          {Array.from({ length: 50 }, (_, i) => 25 + i).map(age => (
                            <option key={age} value={age}>{age}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-bold text-black mb-1">
                          New Face Amount:
                        </label>
                        <input
                          type="number"
                          value={faceAmountData.change_amount}
                          onChange={(e) => updateFormData('change_amount', parseInt(e.target.value) || 0)}
                          className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          step="10000"
                          placeholder="Enter new face amount"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-6">
                <h4 className="font-bold text-black mb-4">b. VARY THE AMOUNT BY Age &lt;SCHEDULE BUTTON&gt;</h4>
                <div>
                  <label className="flex items-center text-black font-semibold mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.want_to_change}
                      onChange={(e) => updateFormData('want_to_change', e.target.checked)}
                      className="mr-2"
                    />
                    Vary the amount by age schedule
                  </label>
                  {faceAmountData.want_to_change && (
                    <div className="mt-2">
                      <button
                        onClick={() => showNotification('Age-based schedule feature will be available soon!')}
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors flex items-center gap-2"
                      >
                        <Calendar className="w-4 h-4" />
                        Schedule
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Section 2: Death Benefit Option A to B Switch */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              2. Your current Death Benefit Option is A (Level). Do you want to switch to option B (level to increasing)?
            </h3>

            <div className="space-y-4">
              <div>
                <h4 className="font-bold text-black mb-4">a. Change to Option B ____ now or starting from age</h4>

                <div className="space-y-4">
                  <label className="flex items-center text-black font-semibold">
                    <input
                      type="checkbox"
                      checked={faceAmountData.switch_immediately}
                      onChange={(e) => updateFormData('switch_immediately', e.target.checked)}
                      className="mr-2"
                    />
                    Change to Option B now
                  </label>

                  <div>
                    <label className="flex items-center text-black font-semibold mb-2">
                      <input
                        type="checkbox"
                        checked={faceAmountData.switch_future_year}
                        onChange={(e) => updateFormData('switch_future_year', e.target.checked)}
                        className="mr-2"
                      />
                      Change to Option B starting from age
                    </label>
                    {faceAmountData.switch_future_year && (
                      <div className="grid grid-cols-2 gap-4 mt-2">
                        <div>
                          <label className="block text-sm font-bold text-black mb-1">Starting Age:</label>
                          <select
                            value={faceAmountData.switch_year}
                            onChange={(e) => updateFormData('switch_year', e.target.value)}
                            className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          >
                            <option value="">Select Age</option>
                            {Array.from({ length: 50 }, (_, i) => 25 + i).map(age => (
                              <option key={age} value={age}>{age}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 3: Death Benefit Option B to A Switch */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              3. Your current Death Benefit Option is B (Increasing). Switch to option A (Increasing to Level)
            </h3>

            <div className="space-y-4">
              <div>
                <h4 className="font-bold text-black mb-4">a. Change to Option A ____ now or starting from age</h4>

                <div className="space-y-4">
                  <label className="flex items-center text-black font-semibold">
                    <input
                      type="checkbox"
                      checked={faceAmountData.option_b_switch_immediately}
                      onChange={(e) => updateFormData('option_b_switch_immediately', e.target.checked)}
                      className="mr-2"
                    />
                    Change to Option A now
                  </label>

                  <div>
                    <label className="flex items-center text-black font-semibold mb-2">
                      <input
                        type="checkbox"
                        checked={faceAmountData.option_b_switch_future}
                        onChange={(e) => updateFormData('option_b_switch_future', e.target.checked)}
                        className="mr-2"
                      />
                      Change to Option A starting from age
                    </label>
                    {faceAmountData.option_b_switch_future && (
                      <div className="grid grid-cols-2 gap-4 mt-2">
                        <div>
                          <label className="block text-sm font-bold text-black mb-1">Starting Age:</label>
                          <select
                            value={faceAmountData.option_b_switch_year}
                            onChange={(e) => updateFormData('option_b_switch_year', e.target.value)}
                            className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          >
                            <option value="">Select Age</option>
                            {Array.from({ length: 50 }, (_, i) => 25 + i).map(age => (
                              <option key={age} value={age}>{age}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          {/* Remove the old grid of four buttons and add new styled buttons like AsIsPage */}
          <div className="flex flex-wrap gap-4 justify-center mb-8">
            <Button
              onClick={saveScenario}
              variant="primary"
              loading={isSaving}
              disabled={isSaving}
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Face Amount Illustration</span>
            </Button>
            <Button
              onClick={() => { setFaceAmountHistory([]); showNotification('All face amount scenarios reset!'); }}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>

          {/* Illustration Results */}
          {showIllustration && (
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-green-500">
              <h3 className="text-2xl font-bold text-black mb-6 flex items-center gap-2">
                <BarChart3 className="w-6 h-6" />
                Face Amount Illustration Results
              </h3>
              
              <div className="mb-6">
                <h4 className="font-bold text-black mb-2">Current Scenario:</h4>
                <p>• Current Face Amount Death Benefit: ${faceAmountData.current_death_benefit.toLocaleString()}</p>

                {(faceAmountData.change_immediately || faceAmountData.change_by_years || faceAmountData.want_to_change) && (
                  <div className="mt-4">
                    <h4 className="font-bold text-black mb-2">Face Amount Changes:</h4>
                    {faceAmountData.change_immediately && (
                      <p>• Change to ${faceAmountData.change_amount.toLocaleString()} now</p>
                    )}
                    {faceAmountData.change_by_years && (
                      <p>• Change to ${faceAmountData.change_amount.toLocaleString()} starting from age {faceAmountData.change_year}</p>
                    )}
                    {faceAmountData.want_to_change && (
                      <p>• Vary amount by age schedule</p>
                    )}
                  </div>
                )}

                {(faceAmountData.switch_immediately || faceAmountData.switch_future_year) && (
                  <div className="mt-4">
                    <h4 className="font-bold text-black mb-2">Option A to B Switch:</h4>
                    {faceAmountData.switch_immediately && <p>• Switch to Option B now</p>}
                    {faceAmountData.switch_future_year && <p>• Switch to Option B starting from age {faceAmountData.switch_year}</p>}
                  </div>
                )}

                {(faceAmountData.option_b_switch_immediately || faceAmountData.option_b_switch_future) && (
                  <div className="mt-4">
                    <h4 className="font-bold text-black mb-2">Option B to A Switch:</h4>
                    {faceAmountData.option_b_switch_immediately && <p>• Switch to Option A now</p>}
                    {faceAmountData.option_b_switch_future && <p>• Switch to Option A starting from age {faceAmountData.option_b_switch_year}</p>}
                  </div>
                )}
              </div>

              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={generateIllustrationData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="year" />
                    <YAxis tickFormatter={(value: number | string) => `$${(Number(value) / 1000).toFixed(0)}k`} />
                    <Tooltip formatter={(value: number | string) => [`$${Number(value).toLocaleString()}`, '']} />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="currentBenefit" 
                      stroke="#2563eb" 
                      strokeWidth={3}
                      name="Current Death Benefit"
                    />
                    {faceAmountData.want_to_change && faceAmountData.change_immediately && (
                      <Line 
                        type="monotone" 
                        dataKey="newBenefit" 
                        stroke="#dc2626" 
                        strokeWidth={3}
                        strokeDasharray="10 5"
                        name="New Death Benefit"
                      />
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}

          {/* Scenario Comparison */}
          {showComparison && (
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-purple-500">
              <h3 className="text-2xl font-bold text-black mb-6 flex items-center gap-2">
                <TrendingUp className="w-6 h-6" />
                Face Amount Scenario Comparison
              </h3>
              
              {faceAmountHistory.length > 0 ? (
                <div className="space-y-4">
                  <h4 className="font-bold text-black">Saved Scenarios:</h4>
                  {faceAmountHistory.map((scenario) => (
                    <div key={scenario.id} className="bg-gray-50 p-4 rounded-lg border">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h5 className="font-bold text-black">Scenario {scenario.id} - {scenario.timestamp}</h5>
                          <p className="text-sm text-gray-600">Customer: {scenario.customer_name}</p>
                          <p className="text-sm text-gray-600">Policy: {scenario.policy_number}</p>
                        </div>
                        <button
                          onClick={() => loadScenario(scenario)}
                          className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors text-sm"
                        >
                          Load Scenario
                        </button>
                      </div>
                      <div>
                        <p className="font-semibold text-black mb-1">Summary:</p>
                        <ul className="text-sm text-gray-700">
                          {scenario.summary.map((item, index) => (
                            <li key={index}>• {item}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <p className="text-gray-600">No saved scenarios available. Save a scenario first to enable comparison.</p>
                </div>
              )}
            </div>
          )}
          </>
        )}
      </div>
    </div>
  );
};

export default FaceAmountPage;