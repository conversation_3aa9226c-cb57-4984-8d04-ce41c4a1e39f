import React, { useState } from 'react';
import { Save, RotateCcw, ArrowRight, Calculator } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const PolicyLapsePage: React.FC = () => {
  const { selectedCustomerData, selectedPolicyData, setActiveTab } = useDashboard();

  // State for Policy Lapse Illustration
  const [lapseModel, setLapseModel] = useState({
    modelPerformance: false,
    untilLapse: false,
    untilSurrender: false,
    whicheverHappens: false,
    surrenderYear: '',
    surrenderAge: '',
    lapseTrigger: false,
    stopPremium: false,
    stopPremiumYear: '',
    stopPremiumAge: '',
    assumeLoan: false,
    maintainMinFunding: false,
    runProjections: false,
    currentRate: '',
    guaranteedRate: '',
    stressRate: '',
    includeRiderCharges: '',
  });

  // State for Guaranteed/Non-Guaranteed Illustrations
  const [nlg, setNlg] = useState({
    runNlg: false,
    nlgProvision: '',
    nlgType: '',
    nlgCustomAge: '',
    nlgPremium: '',
    nlgSchedule: '',
    skipNlg: false,
    modelGuaranteed: false,
    guaranteedMinRate: '',
    guaranteedCharges: false,
    noIndexed: false,
    guaranteedPolicyLoan: false,
    guaranteedSchedule: '',
    skipGuaranteed: false,
    modelNonGuaranteed: false,
    nonGuaranteedRate: '',
    nonGuaranteedCharges: false,
    nonGuaranteedCurrentCharges: false,
    nonGuaranteedCurrentInterest: false,
    nonGuaranteedSchedule: '',
    skipNonGuaranteed: false,
  });

  // Add state for NLG type selection
  const [nlgType, setNlgType] = useState('lifetime');
  const [nlgCustomYears, setNlgCustomYears] = useState('');
  const [nlgCustomAge, setNlgCustomAge] = useState('');

  // Add state for ages for Lifetime and Termed
  const [nlgLifetimeAge, setNlgLifetimeAge] = useState('');
  const [nlgTermedAge, setNlgTermedAge] = useState('');

  // Reset all scenario state
  const handleResetScenarios = () => {
    setLapseModel({
      modelPerformance: false,
      untilLapse: false,
      untilSurrender: false,
      whicheverHappens: false,
      surrenderYear: '',
      surrenderAge: '',
      lapseTrigger: false,
      stopPremium: false,
      stopPremiumYear: '',
      stopPremiumAge: '',
      assumeLoan: false,
      maintainMinFunding: false,
      runProjections: false,
      currentRate: '',
      guaranteedRate: '',
      stressRate: '',
      includeRiderCharges: '',
    });
    setNlg({
      runNlg: false,
      nlgProvision: '',
      nlgType: '',
      nlgCustomAge: '',
      nlgPremium: '',
      nlgSchedule: '',
      skipNlg: false,
      modelGuaranteed: false,
      guaranteedMinRate: '',
      guaranteedCharges: false,
      noIndexed: false,
      guaranteedPolicyLoan: false,
      guaranteedSchedule: '',
      skipGuaranteed: false,
      modelNonGuaranteed: false,
      nonGuaranteedRate: '',
      nonGuaranteedCharges: false,
      nonGuaranteedCurrentCharges: false,
      nonGuaranteedCurrentInterest: false,
      nonGuaranteedSchedule: '',
      skipNonGuaranteed: false,
    });
    alert('All policy lapse and guarantee scenarios have been reset!');
  };

  // Save placeholder
  const handleSave = () => {
    alert('Policy lapse and guarantee illustration saved!');
  };

  // Proceed handler
  const handleProceed = () => {
    setActiveTab('loan-repayment'); // Update this tab name as per your routing/tab logic
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">POLICY LAPSE ILLUSTRATION</h1>
      </div>

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Policy Lapse illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* GUARANTEED / NON-GUARANTEED ILLUSTRATIONS */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">GUARANTEED / NON-GUARANTEED ILLUSTRATIONS</h1>
          </div>
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">2. Do you want to illustrate the policy performance under Guaranteed Assumptions only?</h2>
            <div className="space-y-4">
              <label className="flex items-center text-black font-semibold">
                <input type="checkbox" checked={nlg.modelGuaranteed} onChange={e => setNlg(prev => ({ ...prev, modelGuaranteed: e.target.checked, skipGuaranteed: false }))} className="mr-2" />
                Yes, model the policy performance under guaranteed assumptions
              </label>
              {nlg.modelGuaranteed && (
                <div className="pl-6 space-y-2">
                  <label className="flex items-center text-black">
                    <input type="checkbox" checked={nlg.guaranteedMinRate !== ''} onChange={e => setNlg(prev => ({ ...prev, guaranteedMinRate: e.target.checked ? nlg.guaranteedMinRate : '' }))} className="mr-2" /> Minimum guaranteed interest/crediting rate: <input type="text" value={nlg.guaranteedMinRate} onChange={e => setNlg(prev => ({ ...prev, guaranteedMinRate: e.target.value }))} className="ml-2 w-24 border rounded p-1 text-black bg-blue-100" /> %
                  </label>
                  <label className="flex items-center text-black">
                    <input type="checkbox" checked={nlg.guaranteedCharges} onChange={e => setNlg(prev => ({ ...prev, guaranteedCharges: e.target.checked }))} className="mr-2" /> Maximum allowable charges (cost of insurance, admin charges)
                  </label>
                  <label className="flex items-center text-black">
                    <input type="checkbox" checked={nlg.noIndexed} onChange={e => setNlg(prev => ({ ...prev, noIndexed: e.target.checked }))} className="mr-2" /> No-indexed crediting for IUL/UL – use floor (e.g., 0%)
                  </label>
                  <label className="flex items-center text-black">
                    <input type="checkbox" checked={nlg.guaranteedPolicyLoan} onChange={e => setNlg(prev => ({ ...prev, guaranteedPolicyLoan: e.target.checked }))} className="mr-2" /> Guaranteed policy loan rates (if applicable)
                  </label>
                </div>
              )}
              <div className="font-bold text-black mt-4">Run guaranteed assumptions with which premium schedule?</div>
              <div className="pl-6 space-y-2">
                <label className="flex items-center text-black">
                  <input type="radio" checked={nlg.guaranteedSchedule === 'scheduled'} onChange={() => setNlg(prev => ({ ...prev, guaranteedSchedule: 'scheduled' }))} className="mr-2" /> Scheduled premium
                </label>
                <label className="flex items-center text-black">
                  <input type="radio" checked={nlg.guaranteedSchedule === 'minimum'} onChange={() => setNlg(prev => ({ ...prev, guaranteedSchedule: 'minimum' }))} className="mr-2" /> Minimum funding
                </label>
                <label className="flex items-center text-black">
                  <input type="radio" checked={nlg.guaranteedSchedule === 'catchup'} onChange={() => setNlg(prev => ({ ...prev, guaranteedSchedule: 'catchup' }))} className="mr-2" /> Catch-up premium
                </label>
                <label className="flex items-center text-black">
                  <input type="radio" checked={nlg.guaranteedSchedule === 'custom'} onChange={() => setNlg(prev => ({ ...prev, guaranteedSchedule: 'custom' }))} className="mr-2" /> Custom user-defined schedule
                </label>
              </div>
              <label className="flex items-center text-black font-semibold mt-4">
                <input type="checkbox" checked={nlg.skipGuaranteed} onChange={e => setNlg(prev => ({ ...prev, skipGuaranteed: e.target.checked, modelGuaranteed: false }))} className="mr-2" />
                No, skip guaranteed-only assumptions illustration.
              </label>
            </div>
          </Card>
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">3. Do you want to illustrate the policy performance under Non-Guaranteed Assumptions (current interest, current charges)?</h2>
            <div className="space-y-4">
              <label className="flex items-center text-black font-semibold">
                <input type="checkbox" checked={nlg.modelNonGuaranteed} onChange={e => setNlg(prev => ({ ...prev, modelNonGuaranteed: e.target.checked, skipNonGuaranteed: false }))} className="mr-2" />
                Yes, model non-guaranteed assumptions
              </label>
              {nlg.modelNonGuaranteed && (
                <div className="pl-6 space-y-2">
                  <label className={`flex items-center font-semibold ${nlg.nonGuaranteedRate !== '' ? 'bg-blue-100' : ''} rounded p-1 text-black`}>
                    <input type="checkbox" checked={nlg.nonGuaranteedRate !== ''} onChange={e => setNlg(prev => ({ ...prev, nonGuaranteedRate: e.target.checked ? nlg.nonGuaranteedRate : '' }))} className="mr-2" />
                    Current interest/crediting rate: 
                    <input type="text" value={nlg.nonGuaranteedRate} onChange={e => setNlg(prev => ({ ...prev, nonGuaranteedRate: e.target.value }))} className="ml-2 w-32 border rounded p-1 text-black bg-blue-100" /> %
                  </label>
                  <label className={`flex items-center font-semibold ${nlg.nonGuaranteedCharges ? 'bg-blue-100' : ''} rounded p-1 text-black`}>
                    <input type="checkbox" checked={nlg.nonGuaranteedCharges} onChange={e => setNlg(prev => ({ ...prev, nonGuaranteedCharges: e.target.checked }))} className="mr-2" /> Current policy charges (COI, admin fees, rider charges)
                  </label>
                  <label className={`flex items-center font-semibold ${nlg.nonGuaranteedCurrentCharges ? 'bg-blue-100' : ''} rounded p-1 text-black`}>
                    <input type="checkbox" checked={nlg.nonGuaranteedCurrentCharges} onChange={e => setNlg(prev => ({ ...prev, nonGuaranteedCurrentCharges: e.target.checked }))} className="mr-2" /> Current loan interest rates (if applicable)
                  </label>
                  <label className={`flex items-center font-semibold ${nlg.nonGuaranteedCurrentInterest ? 'bg-blue-100' : ''} rounded p-1 text-black`}>
                    <input type="checkbox" checked={nlg.nonGuaranteedCurrentInterest} onChange={e => setNlg(prev => ({ ...prev, nonGuaranteedCurrentInterest: e.target.checked }))} className="mr-2" /> Current loan interest rates (if applicable)
                  </label>
                </div>
              )}
              <div className="font-bold text-black mt-4">Run non-guaranteed assumptions with which premium schedule?</div>
              <div className="pl-6 space-y-2">
                <label className={`flex items-center font-semibold ${nlg.nonGuaranteedSchedule === 'scheduled' ? 'bg-blue-100' : ''} rounded p-1 text-black`}>
                  <input type="radio" checked={nlg.nonGuaranteedSchedule === 'scheduled'} onChange={() => setNlg(prev => ({ ...prev, nonGuaranteedSchedule: 'scheduled' }))} className="mr-2" /> Scheduled premium
                </label>
                <label className={`flex items-center font-semibold ${nlg.nonGuaranteedSchedule === 'minimum' ? 'bg-blue-100' : ''} rounded p-1 text-black`}>
                  <input type="radio" checked={nlg.nonGuaranteedSchedule === 'minimum'} onChange={() => setNlg(prev => ({ ...prev, nonGuaranteedSchedule: 'minimum' }))} className="mr-2" /> Minimum funding
                </label>
                <label className={`flex items-center font-semibold ${nlg.nonGuaranteedSchedule === 'catchup' ? 'bg-blue-100' : ''} rounded p-1 text-black`}>
                  <input type="radio" checked={nlg.nonGuaranteedSchedule === 'catchup'} onChange={() => setNlg(prev => ({ ...prev, nonGuaranteedSchedule: 'catchup' }))} className="mr-2" /> Catch-up premium
                </label>
                <label className={`flex items-center font-semibold ${nlg.nonGuaranteedSchedule === 'custom' ? 'bg-blue-100' : ''} rounded p-1 text-black`}>
                  <input type="radio" checked={nlg.nonGuaranteedSchedule === 'custom'} onChange={() => setNlg(prev => ({ ...prev, nonGuaranteedSchedule: 'custom' }))} className="mr-2" /> Custom user-defined schedule
                </label>
              </div>
              <label className="flex items-center text-black font-semibold mt-4">
                <input type="checkbox" checked={nlg.skipNonGuaranteed} onChange={e => setNlg(prev => ({ ...prev, skipNonGuaranteed: e.target.checked, modelNonGuaranteed: false }))} className="mr-2" />
                No, skip non-guaranteed-only assumptions illustration.
              </label>
            </div>
          </Card>
          {/* Save and Reset Buttons */}
          <div className="flex justify-center mt-8 gap-4">
            <Button onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
            <Button onClick={handleSave}
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <Save className="w-4 h-4" />
              <span>Save Illustration</span>
            </Button>
            <Button onClick={handleProceed}
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <span>Proceed to Loan Repayment Illustration</span>
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default PolicyLapsePage;