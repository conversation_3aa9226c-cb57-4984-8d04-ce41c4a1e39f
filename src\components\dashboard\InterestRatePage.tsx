import React, { useState } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { Save, RotateCcw, ArrowRight } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

const InterestRatePage: React.FC = () => {
  const { selectedCustomerData, selectedPolicyData, setActiveTab } = useDashboard();
  // Section 1: Interest/Crediting Rate Scenarios
  const [interestScenarioEnabled, setInterestScenarioEnabled] = useState(false);
  const [multiSegmentEnabled, setMultiSegmentEnabled] = useState(false);
  const [currentRate, setCurrentRate] = useState('');
  const [guaranteedRate, setGuaranteedRate] = useState('');
  const [stressRate, setStressRate] = useState('');
  const [userDefinedRate, setUserDefinedRate] = useState('');
  const [conservativeRate, setConservativeRate] = useState('');
  const [expectedRate, setExpectedRate] = useState('');
  const [aggressiveRate, setAggressiveRate] = useState('');
  const [customSegmentRate, setCustomSegmentRate] = useState('');

  // Section 2: Guaranteed Minimum Interest Rate
  const [guaranteedMinEnabled, setGuaranteedMinEnabled] = useState(false);
  const [guaranteedMinRate, setGuaranteedMinRate] = useState('');
  const [guaranteedMinSource, setGuaranteedMinSource] = useState('');

  // Section 3: Cap and Participation Rate Adjustment Scenarios
  const [capScenarioEnabled, setCapScenarioEnabled] = useState(false);
  const [currentCap, setCurrentCap] = useState('');
  const [reducedCap, setReducedCap] = useState('');
  const [increasedCap, setIncreasedCap] = useState('');
  const [variableCap, setVariableCap] = useState('');
  const [currentParticipation, setCurrentParticipation] = useState('');
  const [lowerParticipation, setLowerParticipation] = useState('');
  const [higherParticipation, setHigherParticipation] = useState('');
  const [stepParticipation, setStepParticipation] = useState('');
  const [changeTiming, setChangeTiming] = useState('immediate');
  const [changeAfterYears, setChangeAfterYears] = useState('');
  const [changeGradual, setChangeGradual] = useState('');
  const [changeRandom, setChangeRandom] = useState(false);

  // Reset all scenario state
  const handleResetScenarios = () => {
    setInterestScenarioEnabled(false);
    setMultiSegmentEnabled(false);
    setCurrentRate('');
    setGuaranteedRate('');
    setStressRate('');
    setUserDefinedRate('');
    setConservativeRate('');
    setExpectedRate('');
    setAggressiveRate('');
    setCustomSegmentRate('');
    setGuaranteedMinEnabled(false);
    setGuaranteedMinRate('');
    setGuaranteedMinSource('');
    setCapScenarioEnabled(false);
    setCurrentCap('');
    setReducedCap('');
    setIncreasedCap('');
    setVariableCap('');
    setCurrentParticipation('');
    setLowerParticipation('');
    setHigherParticipation('');
    setStepParticipation('');
    setChangeTiming('immediate');
    setChangeAfterYears('');
    setChangeGradual('');
    setChangeRandom(false);
    alert('All interest rate scenarios have been reset!');
  };

  // Placeholder for schedule button
  const handleSchedule = (section: string) => {
    alert(`Open schedule editor for: ${section}`);
  };

  // Interest rate assumption box
  const InterestBox = () => (
    <div className="p-4 border rounded-lg bg-gray-50 mt-4">
      <div className="font-semibold mb-2 text-black">Run projections under the following interest rate assumptions:</div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
        <Input label="Current crediting rate" value={currentRate} onChange={e => setCurrentRate(e.target.value)} placeholder="%" className="text-black placeholder-black" />
        <Input label="Guaranteed minimum rate" value={guaranteedRate} onChange={e => setGuaranteedRate(e.target.value)} placeholder="%" className="text-black placeholder-black" />
        <Input label="Stress scenario rate" value={stressRate} onChange={e => setStressRate(e.target.value)} placeholder="%" className="text-black placeholder-black" />
      </div>
      <div className="font-semibold mb-2 text-black">Do you want to include rider charges in this scenario?</div>
      <div className="flex items-center space-x-4">
        <label className="flex items-center text-black font-semibold">
          <input type="radio" name="riderCharges" value="yes" /> Yes
        </label>
        <label className="flex items-center text-black font-semibold">
          <input type="radio" name="riderCharges" value="no" /> No
        </label>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">INTEREST RATE ILLUSTRATION</h1>
        <p className="text-gray-600 dark:text-gray-400">Configure interest/crediting rate scenarios for the selected policy.</p>
      </div>

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Interest Rate illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* Section 1: Interest/Crediting Rate Scenarios */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">1. Do you want to model different interest / crediting rate scenarios for the policy?</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={interestScenarioEnabled} onChange={e => setInterestScenarioEnabled(e.target.checked)} />
                <span className="font-semibold text-black">Yes, model different interest/crediting rate scenarios for the policy.</span>
              </div>
              {interestScenarioEnabled && (
                <div className="space-y-4 pl-6">
                  <div className="flex flex-col space-y-2">
                    <Input label="Current interest/crediting rate" value={currentRate} onChange={e => setCurrentRate(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                    <Input label="Guaranteed minimum rate" value={guaranteedRate} onChange={e => setGuaranteedRate(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                    <Input label="Stress scenario rate" value={stressRate} onChange={e => setStressRate(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                    <div className="flex items-center space-x-2">
                      <Input label="User-defined Rates (Reduced/Stress)" value={userDefinedRate} onChange={e => setUserDefinedRate(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                      <Button onClick={() => handleSchedule('User-defined Rates')} className="ml-2">Schedule</Button>
                    </div>
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={multiSegmentEnabled} onChange={e => setMultiSegmentEnabled(e.target.checked)} />
                <span className="font-semibold text-black">Yes, model multiple growth rates should be used for segment comparison</span>
              </div>
              {multiSegmentEnabled && (
                <div className="space-y-2 pl-6">
                  <Input label="Conservative scenario" value={conservativeRate} onChange={e => setConservativeRate(e.target.value)} placeholder="% (e.g., 4%)" className="text-black placeholder-black w-64" />
                  <Input label="Mid/expected scenario" value={expectedRate} onChange={e => setExpectedRate(e.target.value)} placeholder="% (e.g., 5.9%)" className="text-black placeholder-black w-64" />
                  <Input label="Aggressive scenario" value={aggressiveRate} onChange={e => setAggressiveRate(e.target.value)} placeholder="% (e.g., 7%)" className="text-black placeholder-black w-64" />
                  <Input label="Include custom rate (optional)" value={customSegmentRate} onChange={e => setCustomSegmentRate(e.target.value)} placeholder="% (optional)" className="text-black placeholder-black w-64" />
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!interestScenarioEnabled && !multiSegmentEnabled} onChange={e => { setInterestScenarioEnabled(false); setMultiSegmentEnabled(false); }} />
                <span className="font-semibold text-black">No, do not include interest/crediting rate scenario modelling.</span>
              </div>
            </div>
          </Card>
          {/* Section 2: Guaranteed Minimum Interest Rate */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">2. Do you want to model the policy under the Guaranteed Minimum Interest Rate scenario?</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={guaranteedMinEnabled} onChange={e => setGuaranteedMinEnabled(e.target.checked)} />
                <span className="font-semibold text-black">Yes, model the policy under guaranteed minimum interest/crediting rate specified in the policy:</span>
              </div>
              {guaranteedMinEnabled && (
                <div className="space-y-2 pl-6">
                  <Input label="Guaranteed rate" value={guaranteedMinRate} onChange={e => setGuaranteedMinRate(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                  <div className="font-semibold text-black">Source:</div>
                  <div className="flex flex-col space-y-1">
                    <label className="flex items-center text-black"><input type="radio" name="guaranteedMinSource" value="contract" checked={guaranteedMinSource === 'contract'} onChange={e => setGuaranteedMinSource(e.target.value)} /> <span className="ml-2">Stated in contract</span></label>
                    <label className="flex items-center text-black"><input type="radio" name="guaranteedMinSource" value="regulatory" checked={guaranteedMinSource === 'regulatory'} onChange={e => setGuaranteedMinSource(e.target.value)} /> <span className="ml-2">Regulatory minimum</span></label>
                    <label className="flex items-center text-black"><input type="radio" name="guaranteedMinSource" value="indexed" checked={guaranteedMinSource === 'indexed'} onChange={e => setGuaranteedMinSource(e.target.value)} /> <span className="ml-2">Indexed product floor (e.g., 0% in IUL)</span></label>
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!guaranteedMinEnabled} onChange={e => setGuaranteedMinEnabled(false)} />
                <span className="font-semibold text-black">No, do not include guaranteed minimum interest scenario.</span>
              </div>
            </div>
          </Card>
          {/* Section 2b: Interest Box for Multi-segment */}
          <Card className="mb-8">
            <InterestBox />
            <div className="flex items-center space-x-4 mt-2">
              <input type="checkbox" checked={!multiSegmentEnabled} onChange={e => setMultiSegmentEnabled(false)} />
              <span className="font-semibold text-black">No, do not include multi-segment growth rate modelling.</span>
            </div>
          </Card>
          {/* Section 3: Cap and Participation Rate Adjustment Scenarios */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">3. Do you want to model different Cap and Participation Rate Adjustment Scenarios for the Indexed Universal Life (IUL) policy?</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={capScenarioEnabled} onChange={e => setCapScenarioEnabled(e.target.checked)} />
                <span className="font-semibold text-black">Yes, model cap & Participation rate scenarios for the IUL policy</span>
              </div>
              {capScenarioEnabled && (
                <div className="space-y-2 pl-6">
                  <Input label="Current cap rate" value={currentCap} onChange={e => setCurrentCap(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                  <Input label="Reduced cap rate" value={reducedCap} onChange={e => setReducedCap(e.target.value)} placeholder="% (e.g., drop to 8%)" className="text-black placeholder-black w-64" />
                  <Input label="Increased cap rate (if possible)" value={increasedCap} onChange={e => setIncreasedCap(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                  <Input label="Variable cap rate by year (e.g., drop from 10% to 7% over 5 years)" value={variableCap} onChange={e => setVariableCap(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                  <div className="font-semibold text-black mt-2">What participation rate scenarios should be modelled?</div>
                  <Input label="Current participation rate" value={currentParticipation} onChange={e => setCurrentParticipation(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                  <Input label="Lower participation rate" value={lowerParticipation} onChange={e => setLowerParticipation(e.target.value)} placeholder="% (e.g., drop from 100% to 80%)" className="text-black placeholder-black w-64" />
                  <Input label="Higher participation rate (if product allows)" value={higherParticipation} onChange={e => setHigherParticipation(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                  <Input label="Step-change or year-by-year custom schedule" value={stepParticipation} onChange={e => setStepParticipation(e.target.value)} placeholder="%" className="text-black placeholder-black w-64" />
                  <div className="font-semibold text-black mt-2">Should these changes be modelled</div>
                  <div className="flex flex-col space-y-1">
                    <label className="flex items-center text-black"><input type="radio" name="changeTiming" value="immediate" checked={changeTiming === 'immediate'} onChange={e => setChangeTiming(e.target.value)} /> <span className="ml-2">Immediately (Year 1 onward)</span></label>
                    <label className="flex items-center text-black"><input type="radio" name="changeTiming" value="after-years" checked={changeTiming === 'after-years'} onChange={e => setChangeTiming(e.target.value)} /> <span className="ml-2">After <Input value={changeAfterYears} onChange={e => setChangeAfterYears(e.target.value)} placeholder="years" className="text-black placeholder-black w-20 inline-block" /> years of current rates</span></label>
                    <label className="flex items-center text-black"><input type="radio" name="changeTiming" value="gradual" checked={changeTiming === 'gradual'} onChange={e => setChangeTiming(e.target.value)} /> <span className="ml-2">Gradually (e.g., 1% cap drop per year) <Input value={changeGradual} onChange={e => setChangeGradual(e.target.value)} placeholder="% per year" className="text-black placeholder-black w-24 inline-block" /></span></label>
                    <label className="flex items-center text-black"><input type="radio" name="changeTiming" value="random" checked={changeTiming === 'random'} onChange={e => setChangeTiming(e.target.value)} /> <span className="ml-2">Random/alternating (simulate volatility)</span></label>
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!capScenarioEnabled} onChange={e => setCapScenarioEnabled(false)} />
                <span className="font-semibold text-black">No, do not include cap/participation adjustment modelling.</span>
              </div>
            </div>
          </Card>
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={() => alert('AS-IS configuration saved successfully and added to Selected Scenarios!')}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Interest Rate Illustration</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
            <Button
              onClick={() => setActiveTab('loan-repayment-illustration')}
              className="flex items-center space-x-2"
            >
              <span>Proceed to Loan Repayment Illustration</span>
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default InterestRatePage;