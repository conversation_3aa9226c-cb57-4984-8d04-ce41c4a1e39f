import React from 'react';
import {
  BarChart3,
  FileText,
  Settings,
  TrendingUp,
  Shield,
  LogOut,
  Bookmark
} from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import { useAuth } from '../../contexts/AuthContext';

const Sidebar: React.FC = () => {
  const { activeTab, setActiveTab } = useDashboard();
  const { logout, user } = useAuth();

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard Overview', icon: BarChart3, description: 'Main dashboard with key metrics' },
    { id: 'policy-selection', label: 'Policy Selection', icon: Shield, description: 'Select and configure policies' },
  ];

  const bottomMenuItems = [
    { id: 'selected-scenarios', label: 'Selected Illustrations', icon: Bookmark, description: 'View saved scenarios' },
    { id: 'analysis-reports', label: 'Illustration Reports', icon: FileText, description: 'Generate and view reports' },
    { id: 'settings', label: 'Settings', icon: Settings, description: 'Application settings' },
  ];

  const handleIllustrationClick = () => {
    // Navigate directly to the illustration main page (defaults to AS-IS)
    setActiveTab('as-is');
  };

  return (
    <div className="w-64 bg-gray-900 dark:bg-[#121212] text-white h-screen flex flex-col transition-all duration-300 shadow-lg dark:shadow-2xl">
      <div className="p-6 border-b border-gray-800">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-600 dark:bg-blue-500 rounded-lg flex items-center justify-center">
            <Shield className="w-6 h-6" />
          </div>
          <div>
            <h2 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Life Insurance
            </h2>
            <p className="text-sm text-blue-200 font-medium">Illustration Portal</p>
          </div>
        </div>
      </div>

      <div className="flex-1 py-6 overflow-y-auto scrollbar-thin scrollbar-track-gray-800 scrollbar-thumb-gray-600">
        <nav className="space-y-2 px-3">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left transition-all duration-200 group ${
                  activeTab === item.id
                    ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-lg'
                    : 'text-white/70 hover:bg-gray-800 dark:hover:bg-gray-700 hover:text-white'
                }`}
                title={item.description}
              >
                <Icon className={`w-5 h-5 ${activeTab === item.id ? 'text-white' : 'group-hover:text-blue-400'} transition-colors`} />
                <div className="flex-1">
                  <span className="font-medium">{item.label}</span>
                  {activeTab !== item.id && (
                    <p className="text-xs text-white/50 group-hover:text-white/70 transition-colors">
                      {item.description}
                    </p>
                  )}
                </div>
              </button>
            );
          })}

          {/* Illustration Manager - Single Button */}
          <button
            onClick={handleIllustrationClick}
            className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left transition-all duration-200 group ${
              activeTab === 'illustrations' ||
              ['as-is', 'face-amount', 'premium', 'income', 'loan-repayment', 'interest-rate', 'policy-lapse'].includes(activeTab)
                ? 'bg-gradient-to-r from-green-600 to-emerald-500 text-white shadow-lg'
                : 'text-white/70 hover:bg-gray-800 dark:hover:bg-gray-700 hover:text-white'
            }`}
            title="Create and manage policy illustrations"
          >
            <TrendingUp className={`w-5 h-5 ${
              ['illustrations', 'as-is', 'face-amount', 'premium', 'income', 'loan-repayment', 'interest-rate', 'policy-lapse'].includes(activeTab)
                ? 'text-white'
                : 'group-hover:text-green-400'
            } transition-colors`} />
            <div className="flex-1">
              <span className="font-medium">Policy Illustration</span>
              {!['illustrations', 'as-is', 'face-amount', 'premium', 'income', 'loan-repayment', 'interest-rate', 'policy-lapse'].includes(activeTab) && (
                <p className="text-xs text-white/50 group-hover:text-white/70 transition-colors">
                  Create and manage scenarios
                </p>
              )}
            </div>
          </button>

          {bottomMenuItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left transition-all duration-200 group ${
                  activeTab === item.id
                    ? 'bg-gradient-to-r from-purple-600 to-indigo-500 text-white shadow-lg'
                    : 'text-white/70 hover:bg-gray-800 dark:hover:bg-gray-700 hover:text-white'
                }`}
                title={item.description}
              >
                <Icon className={`w-5 h-5 ${activeTab === item.id ? 'text-white' : 'group-hover:text-purple-400'} transition-colors`} />
                <div className="flex-1">
                  <span className="font-medium">{item.label}</span>
                  {activeTab !== item.id && (
                    <p className="text-xs text-white/50 group-hover:text-white/70 transition-colors">
                      {item.description}
                    </p>
                  )}
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      <div className="p-6 border-t border-gray-800 dark:border-gray-700">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gray-700 dark:bg-gray-600 rounded-full flex items-center justify-center shadow-lg">
            <span className="text-sm font-medium">
              {user?.name?.split(' ').map(n => n[0]).join('') || 'U'}
            </span>
          </div>
          <div>
            <p className="text-sm font-medium">{user?.name}</p>
            <p className="text-xs text-white/50">{user?.email}</p>
          </div>
        </div>
        <button
          onClick={logout}
          className="w-full flex items-center space-x-3 px-3 py-2 text-white/70 hover:bg-gray-800 dark:hover:bg-gray-700 hover:text-white rounded-lg transition-all duration-200"
        >
          <LogOut className="w-4 h-4" />
          <span>Sign Out</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;